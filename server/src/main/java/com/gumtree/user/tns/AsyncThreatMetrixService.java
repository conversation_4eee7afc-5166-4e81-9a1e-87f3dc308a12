package com.gumtree.user.tns;

import com.gumtree.gumshieldapi.api.ChecklistManagementApi;
import com.gumtree.gumshieldapi.model.ApiCheckValue;
import com.gumtree.gumshieldapi.model.ApiChecklistAttribute;
import com.gumtree.gumshieldapi.model.ApiChecklistEntry;
import com.gumtree.gumshieldapi.model.ApiChecklistType;
import com.gumtree.gumshieldapi.model.ChecklistEntryRequest;
import com.gumtree.threatmetrix.api.SessionApi;
import com.gumtree.threatmetrix.model.LoginSession;
import com.gumtree.threatmetrix.model.RegistrationSession;
import com.gumtree.user.spring.config.global.Metrics;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.util.Assert;

import java.util.Map;

import static com.google.common.primitives.Ints.tryParse;

@SuppressWarnings("UnstableApiUsage")
public class AsyncThreatMetrixService {
    private static final Logger LOGGER = LoggerFactory.getLogger(AsyncThreatMetrixService.class);
    private static final String SCORE_FIELD = "policy_score";
    private static final String REQUEST_RESULT_FIELD = "request_result";
    private static final int BANNED_RISK_SCORE_THRESHOLD = -100;
    private static final DateTimeFormatter NOTE_DATE_TIME_FORMATTER =
            DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ssZZ").withZone(DateTimeZone.forID("Europe/London"));

    @Autowired
    private SessionApi sessionApi;
    @Autowired
    private ChecklistManagementApi checklistManagementApi;

    @Async
    public void collectLoginTrackingData(String sessionId, LoginSession sessionTracking) {
        try {
            handleCollectLoginTrackingData(sessionId, sessionTracking);
        } catch (Exception exception) {
            LOGGER.error("Failed to collect login tracking data for sessionId:{} ", sessionId, exception);
        }
    }

    private void handleCollectLoginTrackingData(String sessionId, LoginSession sessionTracking) {
        validateRequiredFields(
                sessionTracking.getAccountEmail(),
                sessionTracking.getUserId()
        );

        if (!isSessionIdPresent(sessionId)) {
            return;
        }

        Map<String, Object> deviceData = sessionApi.loginSession(sessionId, sessionTracking, true)
                .toBlocking()
                .value();
        LOGGER.warn("TMX-login: sessionId: {}, userId:{} , response: {}",sessionId,sessionTracking.getUserId(),deviceData);
        handleDeviceData(deviceData, sessionTracking.getAccountEmail());
    }

    @Async
    public void collectRegistrationTrackingData(String sessionId, RegistrationSession sessionTracking) {
        try {
            handleCollectRegistrationTrackingData(sessionId, sessionTracking);
        } catch (Exception exception) {
            LOGGER.error("Failed to collect registration tracking data for sessionId:{} ", sessionId, exception);
        }
    }

    private void handleCollectRegistrationTrackingData(String sessionId, RegistrationSession sessionTracking) {
        validateRequiredFields(
                sessionTracking.getAccountEmail(),
                sessionTracking.getUserId()
        );

        if (!isSessionIdPresent(sessionId)) {
            return;
        }

        Map<String, Object> deviceData = sessionApi.registrationSession(sessionId, sessionTracking)
                .toBlocking()
                .value();

        LOGGER.warn("TMX-register: sessionId: {}, userId:{} , response: {}",sessionId,sessionTracking.getUserId(),deviceData);
        handleDeviceData(deviceData, sessionTracking.getAccountEmail());
    }

    private void validateRequiredFields(String accountEmail, Long userId) {
        Assert.notNull(accountEmail);
        Assert.notNull(userId);
    }

    private boolean isSessionIdPresent(String sessionId) {
        if (StringUtils.isBlank(sessionId)) {
            Metrics.getInstance().ifPresent(Metrics::incEmptyThreatMetrixSessionId);
            return false;
        }
        return true;
    }

    private void handleDeviceData(Map<String, Object> deviceData, String accountEmail) {
        if (isSuccessful(deviceData)) {
            adUserToBlockedChecklistIfNotTrustedSafely(accountEmail, deviceData);
        } else {
            LOGGER.info("Unsuccessful attempt to collect device tracking data. ThreatMetrix Response = {}", deviceData);
        }
    }

    private boolean isSuccessful(Map<String, Object> deviceData) {
        return deviceData.containsKey(REQUEST_RESULT_FIELD) && "success".equals(deviceData.get(REQUEST_RESULT_FIELD));
    }

    private void adUserToBlockedChecklistIfNotTrustedSafely(String email, Map<String, Object> deviceData) {
        if (isTrustedDevice(deviceData)) {
            return;
        }

        try {
            ApiCheckValue apiCheckValue = new ApiCheckValue();
            apiCheckValue.setValue(email);
            ApiChecklistEntry alreadyBlocked = checklistManagementApi.findEntryByValue(ApiChecklistType.BLACK, ApiChecklistAttribute.EMAIL,
                    apiCheckValue).toBlocking().value();

            if (alreadyBlocked != null) {
                return;
            }

            ChecklistEntryRequest blockedEntry = new ChecklistEntryRequest()
                    .type(ApiChecklistType.BLACK)
                    .attribute(ApiChecklistAttribute.EMAIL)
                    .value(email)
                    .notes("TMX banned the account " + DateTime.now().toString(NOTE_DATE_TIME_FORMATTER));


            checklistManagementApi.createEntry(blockedEntry).toBlocking().value();
        } catch (Exception e) {
            LOGGER.error("Gumshiled API get/create entry failed", e);
        }
    }

    private static boolean isTrustedDevice(Map<String, Object> deviceData) {
        Integer score = deviceData.get(SCORE_FIELD) == null ? null : tryParse(String.valueOf(deviceData.get(SCORE_FIELD)));
        return score == null || score > BANNED_RISK_SCORE_THRESHOLD;
    }
}
