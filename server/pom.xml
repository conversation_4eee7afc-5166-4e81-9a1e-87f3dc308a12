<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>user-service</artifactId>
        <groupId>com.gumtree.user</groupId>
        <version>3.0-SNAPSHOT</version>
    </parent>
    <artifactId>server</artifactId>
    <packaging>war</packaging>
    <name>user-server</name>
    <description>Gumtree User Service</description>

    <properties>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>

        <dependency>
            <groupId>com.gumtree.bapi</groupId>
            <artifactId>bapi-contract</artifactId>
            <type>yaml</type>
        </dependency>
        <dependency>
            <groupId>gumtree-gumshield-api</groupId>
            <artifactId>gumshield-api-contract</artifactId>
            <type>yaml</type>
        </dependency>
        <dependency>
            <groupId>com.gumtree.tns.threatmetrix</groupId>
            <artifactId>contract</artifactId>
            <type>yaml</type>
        </dependency>
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-annotations</artifactId>
            <version>1.6.0</version>
        </dependency>

        <dependency>
            <groupId>com.gumtree.shared-commons.jetty</groupId>
            <artifactId>jetty-server-legacy</artifactId>
        </dependency>

        <dependency>
            <groupId>com.gumtree.shared-commons.properties</groupId>
            <artifactId>gtprops</artifactId>
        </dependency>

        <dependency>
            <groupId>xmlunit</groupId>
            <artifactId>xmlunit</artifactId>
            <version>1.5</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>user-model</artifactId>
        </dependency>

        <!-- Kafka dependencies -->
        <dependency>
            <groupId>org.apache.kafka</groupId>
            <artifactId>kafka-clients</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.avro</groupId>
            <artifactId>avro</artifactId>
        </dependency>
        <dependency>
            <groupId>io.confluent</groupId>
            <artifactId>kafka-avro-serializer</artifactId>
        </dependency>

        <!-- Avro schemas -->
        <dependency>
            <groupId>com.gumtree.schemas.user</groupId>
            <artifactId>value</artifactId>
        </dependency>

        <!-- Google library -->
        <dependency>
            <groupId>com.google.apis</groupId>
            <artifactId>google-api-services-oauth2</artifactId>
            <version>v2-rev114-1.22.0</version>
            <exclusions>
                <exclusion>
                    <groupId>com.google.code.findbugs</groupId>
                    <artifactId>jsr305</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.google.code.findbugs</groupId>
            <artifactId>jsr305</artifactId>
        </dependency>
        <dependency>
            <groupId>javax.annotation</groupId>
            <artifactId>javax.annotation-api</artifactId>
        </dependency>

        <!-- Spring -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context-support</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webmvc</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-jdbc</artifactId>
        </dependency>

        <!-- Resteasy -->
        <dependency>
            <groupId>org.jboss.resteasy</groupId>
            <artifactId>resteasy-jaxrs</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>commons-io</groupId>
                    <artifactId>commons-io</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.jboss.resteasy</groupId>
            <artifactId>resteasy-jackson-provider</artifactId>
        </dependency>

        <!-- jackson 1.9 -->
        <dependency>
            <groupId>org.codehaus.jackson</groupId>
            <artifactId>jackson-core-asl</artifactId>
        </dependency>
        <dependency>
            <groupId>org.codehaus.jackson</groupId>
            <artifactId>jackson-mapper-asl</artifactId>
        </dependency>

        <!-- use to validate requests @Valid, @NotNull etc -->
        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-validator</artifactId>
        </dependency>

        <dependency>
            <groupId>spy</groupId>
            <artifactId>spymemcached</artifactId>
        </dependency>

        <!-- apache commons libraries -->
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>

        <dependency>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging</artifactId>
        </dependency>

        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-registry-prometheus</artifactId>
        </dependency>
        <dependency>
            <groupId>io.prometheus</groupId>
            <artifactId>simpleclient_servlet</artifactId>
        </dependency>

        <!-- logging -->
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
        </dependency>

        <!-- Feign Client -->
        <dependency>
            <groupId>com.gumtree</groupId>
            <artifactId>shared-feign-hystrix-api-client</artifactId>
        </dependency>


        <!-- Properties -->
        <dependency>
            <groupId>com.netflix.archaius</groupId>
            <artifactId>archaius-core</artifactId>
        </dependency>

        <!-- Hystrix -->
        <dependency>
            <groupId>com.netflix.hystrix</groupId>
            <artifactId>hystrix-core</artifactId>
        </dependency>

        <!-- Database -->
        <dependency>
            <groupId>com.zaxxer</groupId>
            <artifactId>HikariCP</artifactId>
        </dependency>

        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
        </dependency>

        <!-- Feign dependencies -->
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-core</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-hystrix</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-slf4j</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-jackson</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-okhttp</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.openfeign.form</groupId>
            <artifactId>feign-form</artifactId>
        </dependency>

        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
        </dependency>
        <dependency>
            <groupId>com.squareup.okio</groupId>
            <artifactId>okio</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.oltu.oauth2</groupId>
            <artifactId>org.apache.oltu.oauth2.client</artifactId>
        </dependency>

        <dependency>
            <groupId>org.openapitools</groupId>
            <artifactId>jackson-databind-nullable</artifactId>
            <version>0.2.1</version>
        </dependency>

        <!-- testing -->
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-engine</artifactId>
        </dependency>
        <dependency>
            <groupId>org.junit.vintage</groupId>
            <artifactId>junit-vintage-engine</artifactId>
        </dependency>
        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>testcontainers</artifactId>
        </dependency>
        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>junit-jupiter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-params</artifactId>
        </dependency>
        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>postgresql</artifactId>
        </dependency>

        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
        </dependency>
        <dependency>
            <groupId>org.hamcrest</groupId>
            <artifactId>hamcrest</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.tomakehurst</groupId>
            <artifactId>wiremock-standalone</artifactId>
        </dependency>

        <!-- fluent api assertion for nice readable tests -->
        <dependency>
            <groupId>org.easytesting</groupId>
            <artifactId>fest-assert-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.gumtree.user</groupId>
            <artifactId>db</artifactId>
            <version>${gt.version}</version> <!-- TODO: fix once user service is removed from delivery pipeline -->
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.gumtree.shared-commons.properties</groupId>
            <artifactId>gtprops-spring</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-beanutils</groupId>
            <artifactId>commons-beanutils</artifactId>
            <version>1.9.4</version>
        </dependency>

        <dependency>
            <groupId>com.github.tomakehurst</groupId>
            <artifactId>wiremock-jre8</artifactId>
            <version>2.26.3</version>
            <scope>test</scope>
        </dependency>

        <!-- JWT Validation-->
        <dependency>
            <groupId>com.auth0</groupId>
            <artifactId>java-jwt</artifactId>
        </dependency>

        <dependency>
            <groupId>com.auth0</groupId>
            <artifactId>jwks-rsa</artifactId>
        </dependency>

    </dependencies>

    <build>
        <finalName>user-service</finalName>
        <plugins>
            <plugin>
                <artifactId>maven-war-plugin</artifactId>
                <executions>
                    <execution>
                        <id>default-war</id>
                        <phase>none</phase>
                    </execution>
                    <execution>
                        <id>package-jetty-war</id>
                        <phase>package</phase>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>unpack-shared-resources</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>unpack-dependencies</goal>
                        </goals>
                        <configuration>
                            <includeGroupIds>com.gumtree</includeGroupIds>
                            <includeArtifactIds>shared-feign-hystrix-api-client</includeArtifactIds>
                            <outputDirectory>${project.build.directory}/shared-resources</outputDirectory>
                            <includes>**/*.mustache</includes>
                        </configuration>
                    </execution>
                    <!-- Copy contract for client generation -->
                    <execution>
                        <id>copy</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <includeTypes>yaml</includeTypes>
                            <outputDirectory>${project.build.directory}/contracts</outputDirectory>
                            <stripVersion>true</stripVersion>
                            <prependGroupId>true</prependGroupId>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.openapitools</groupId>
                <artifactId>openapi-generator-maven-plugin</artifactId>
                <version>4.2.2</version>
                <executions>
                    <execution>
                        <id>bapi-service-contract</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>${project.build.directory}/contracts/com.gumtree.bapi.bapi-contract.yaml</inputSpec>
                            <generatorName>java</generatorName>
                            <configOptions>
                                <java8>true</java8>
                                <dateLibrary>java</dateLibrary>
                                <library>feign</library>
                            </configOptions>
                            <templateDirectory>${project.build.directory}/shared-resources/Java.libraries.feign</templateDirectory>
                            <supportingFilesToGenerate>EncodingUtils.java,ParamExpander.java,RFC3339DateFormat.java,StringUtil.java,ApiClient.java,ApiKeyAuth.java,HttpBearerAuth.java,HttpBasicAuth.java</supportingFilesToGenerate>
                            <output>${project.build.directory}/generated-sources</output>
                            <apiPackage>com.gumtree.bapi.service.api</apiPackage>
                            <modelPackage>com.gumtree.bapi.service.model</modelPackage>
                            <generateApis>true</generateApis>
                            <generateApiTests>false</generateApiTests>
                            <generateModelTests>false</generateModelTests>
                            <invokerPackage>com.gumtree.bapi.service.api</invokerPackage>
                            <httpUserAgent>bapi-generated-client</httpUserAgent>
                            <enablePostProcessFile>true</enablePostProcessFile>
                            <library>feign</library>
                        </configuration>
                    </execution>
                    <execution>
                        <id>gumshield-api-contract</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>${project.build.directory}/contracts/gumtree-gumshield-api.gumshield-api-contract.yaml</inputSpec>
                            <generatorName>java</generatorName>
                            <configOptions>
                                <java8>true</java8>
                                <dateLibrary>java</dateLibrary>
                                <library>feign</library>
                            </configOptions>
                            <templateDirectory>${project.build.directory}/shared-resources/Java.libraries.feign</templateDirectory>
                            <supportingFilesToGenerate>EncodingUtils.java,ParamExpander.java,RFC3339DateFormat.java,StringUtil.java,ApiClient.java,ApiKeyAuth.java,HttpBearerAuth.java,HttpBasicAuth.java</supportingFilesToGenerate>
                            <output>${project.build.directory}/generated-sources</output>
                            <apiPackage>com.gumtree.gumshieldapi.api</apiPackage>
                            <modelPackage>com.gumtree.gumshieldapi.model</modelPackage>
                            <generateApis>true</generateApis>
                            <generateApiTests>false</generateApiTests>
                            <generateModelTests>false</generateModelTests>
                            <invokerPackage>com.gumtree.gumshieldapi.api</invokerPackage>
                            <httpUserAgent>user service [gumshieldapi-generated-client]</httpUserAgent>
                            <enablePostProcessFile>true</enablePostProcessFile>
                            <library>feign</library>
                        </configuration>
                    </execution>

                    <execution>
                        <id>threatmetrix-api-contract</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>${project.build.directory}/contracts/com.gumtree.tns.threatmetrix.contract.yaml</inputSpec>
                            <generatorName>java</generatorName>
                            <configOptions>
                                <java8>true</java8>
                                <dateLibrary>java</dateLibrary>
                                <library>feign</library>
                            </configOptions>
                            <templateDirectory>${project.build.directory}/shared-resources/Java.libraries.feign</templateDirectory>
                            <supportingFilesToGenerate>EncodingUtils.java,ParamExpander.java,RFC3339DateFormat.java,StringUtil.java,ApiClient.java,ApiKeyAuth.java,HttpBearerAuth.java,HttpBasicAuth.java</supportingFilesToGenerate>
                            <output>${project.build.directory}/generated-sources</output>
                            <apiPackage>com.gumtree.threatmetrix.api</apiPackage>
                            <modelPackage>com.gumtree.threatmetrix.model</modelPackage>
                            <generateApis>true</generateApis>
                            <generateApiTests>false</generateApiTests>
                            <generateModelTests>false</generateModelTests>
                            <invokerPackage>com.gumtree.threatmetrix.api</invokerPackage>
                            <httpUserAgent>threatmetrix-generated-client</httpUserAgent>
                            <enablePostProcessFile>true</enablePostProcessFile>
                            <library>feign</library>
                        </configuration>
                    </execution>

                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.22.2</version>
                <dependencies>
                    <dependency>
                        <groupId>org.junit.jupiter</groupId>
                        <artifactId>junit-jupiter-engine</artifactId>
                        <version>${junit5.version}</version>
                    </dependency>
                </dependencies>
            </plugin>
        </plugins>
    </build>

</project>
